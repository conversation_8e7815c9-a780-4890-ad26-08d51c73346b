# TODO
1. 我在构想一种极简化的设置流程
2. 因为我的产品功能尽可能保持简单，我思考我的设置项目其实能够提供的设置并不多 
3. 我目前可能唯一需要的就是用户的设置：学习语言和本地语言。但是，本地语言可以根据用户当时设备上的语言进行推断，所以唯一需要确定的就是用户想要学习的语言，比如英语、西班牙语、日语、韩语或德语。这些设置的含义是用户想要学习的语言。
4. 我在想，我也没有其他设置可以提供。所以，我在考虑能不能将设置页面和下拉搜索页面整合到一起，例如在搜索框的下边或右侧提供一个学习语言的下拉列表。点击列表后，会自动下载语言包；下载成功之后，就能搜索单词，并出现搜索建议，这些都在本地设备中发生。
5. 不需要注册，所有的收藏也完全在本地，所以不会将用户的数据上传到云端记录和保留。那是否意味着像隐私政策或用户协议等其实都不需要编写？然后也不需要设置登录。因此，能够提供的设置选项真的并不多。
6. 我目前唯一确定的设置项目就是：学习语言 + 付费订阅，我希望能够以非传统的方式自然地整合到应用的使用过程当中，而不是突兀地构建一个设置页面，你认为是否可行。