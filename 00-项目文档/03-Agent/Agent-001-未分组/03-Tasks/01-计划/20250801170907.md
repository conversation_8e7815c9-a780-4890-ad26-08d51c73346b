# TODO
1. 我在构想一种极简化的设置流程
2. 因为我的产品功能尽可能保持简单，我思考我的设置项目其实能够提供的设置并不多
3. 我目前可能唯一需要的就是用户的设置：学习语言和本地语言。但是，本地语言可以根据用户当时设备上的语言进行推断，所以唯一需要确定的就是用户想要学习的语言，比如英语、西班牙语、日语、韩语或德语。这些设置的含义是用户想要学习的语言。
4. 我在想，我也没有其他设置可以提供。所以，我在考虑能不能将设置页面和下拉搜索页面整合到一起，例如在搜索框的下边或右侧提供一个学习语言的下拉列表。点击列表后，会自动下载语言包；下载成功之后，就能搜索单词，并出现搜索建议，这些都在本地设备中发生。
5. 不需要注册，所有的收藏也完全在本地，所以不会将用户的数据上传到云端记录和保留。那是否意味着像隐私政策或用户协议等其实都不需要编写？然后也不需要设置登录。因此，能够提供的设置选项真的并不多。
6. 我目前唯一确定的设置项目就是：学习语言 + 付费订阅，我希望能够以非传统的方式自然地整合到应用的使用过程当中，而不是突兀地构建一个设置页面，你认为是否可行。

## AI 分析

### 项目架构深度分析

通过深入分析SenseWord项目的代码库和需求文档，我发现这是一个采用**云端无状态、本地优先**架构的英语学习应用，具有以下核心特征：

#### 1. 技术架构特点
- **TADA架构**：Translation-Adapter & Domain-Adapter双层服务架构
- **无状态后端**：纯静态CDN内容分发，无用户状态管理
- **本地优先**：SQLite + UserDefaults + Keychain三层本地存储
- **Apple生态深度集成**：StoreKit 2支付、CloudKit同步、Apple ID认证

#### 2. 当前设置系统现状
项目已实现完整的设置系统：
- **SettingsService**：管理用户偏好设置
- **语言选择**：支持多语言切换（LanguageSelectionView）
- **音标偏好**：美式/英式音标选择
- **个性化设置**：自动播放、触感反馈、通知等
- **订阅管理**：基于StoreKit 2的匿名购买系统

#### 3. 核心设计哲学
- **极简主义**：消除非核心复杂性，专注学习体验
- **无身份注册**：完全匿名使用，仅在付费时通过Apple ID验证
- **本地数据主权**：用户完全拥有自己的数据
- **自然集成**：设置融入使用流程，避免突兀的设置页面

#### 4. 用户体验设计
- **句子解构学习法**：通过左滑分解句子，深度理解语境
- **无限内容流**：基于个人学习轨迹的智能推荐
- **智能搜索**：预生成内容库，即时响应无等待
- **优雅付费转化**：基于价值认知的自然付费体验

### 关键发现

1. **设置系统已经很完善**：当前的设置系统已经实现了语言选择、音标偏好、个性化设置等功能
2. **搜索界面已有语言感知**：SearchView通过SettingsService获取用户偏好语言
3. **架构支持极简设置**：无状态架构天然支持最小化设置项
4. **自然集成机会**：可以将语言选择集成到搜索界面，实现更自然的用户体验

## CML任务清单

### 阶段一：搜索界面语言选择集成
- [ ] 在SearchView.swift的searchInputBar区域添加语言选择按钮：在搜索框右侧添加当前语言显示按钮（如"EN"），点击触发语言选择
- [ ] 在SearchView.swift中添加语言选择状态管理：添加@State private var showLanguageSelection: Bool = false状态变量
- [ ] 在SearchView.swift中集成LanguageSelectionView：使用.sheet修饰符展示语言选择界面，传递当前语言和选择回调
- [ ] 在SearchViewModel.swift中添加语言切换方法：添加updateLanguage(_ language: LanguageCode)方法，调用SettingsService更新语言设置
- [ ] 在SearchView.swift中添加语言切换响应：监听语言变化，自动触发索引下载和搜索建议更新

### 阶段二：首次使用语言引导优化
- [ ] 在MainContentView.swift中添加首次启动检测：检查UserDefaults中是否存在语言设置，未设置时显示语言选择引导
- [ ] 创建LanguageOnboardingView.swift：设计简洁的首次语言选择界面，突出"选择学习语言"概念
- [ ] 在LanguageOnboardingView.swift中实现语言包下载提示：选择语言后显示"正在准备您的学习内容..."加载状态
- [ ] 在MainContentView.swift中集成语言引导流程：首次启动时显示LanguageOnboardingView，完成后进入正常界面

### 阶段三：设置页面简化优化
- [ ] 在SettingsView.swift中简化语言设置区域：移除冗余的语言设置项，保留音标偏好设置
- [ ] 在SettingsView.swift中优化订阅管理区域：突出Premium功能价值，简化订阅状态显示
- [ ] 在SettingsView.swift中移除非核心设置项：评估并移除不必要的设置选项，保持极简风格
- [ ] 在SettingsView.swift中添加"在搜索时切换语言"提示：引导用户通过搜索界面进行语言切换

### 阶段四：用户体验优化
- [ ] 在SearchView.swift中添加语言切换动画：语言选择后添加平滑的界面更新动画
- [ ] 在LocalIndexService.swift中优化语言包下载体验：添加下载进度提示和错误处理
- [ ] 在SearchView.swift中添加语言状态指示：显示当前语言的索引状态（已下载/下载中/未下载）
- [ ] 在MainContentView.swift中添加语言切换后的内容更新：确保语言切换后推荐内容和搜索建议正确更新